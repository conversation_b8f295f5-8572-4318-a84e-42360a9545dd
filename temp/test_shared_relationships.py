#!/usr/bin/env python3
"""
测试 shared_relationships 表的插入功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.repositories.task_repository import TaskRepository
import uuid

def test_shared_relationships():
    """测试共享关系数据插入"""
    print("🧪 测试 shared_relationships 表插入功能...")
    
    task_repo = TaskRepository()
    
    # 创建测试任务
    test_task_id = str(uuid.uuid4())
    try:
        task_repo.create_task(test_task_id, 'test_agent_analysis', 'test_data.csv')
        print(f"✅ 测试任务创建成功: {test_task_id}")
    except Exception as e:
        print(f"❌ 测试任务创建失败: {e}")
        return False
    
    # 准备测试数据
    test_relationships = [
        {
            'relationship_type': 'device',
            'shared_value': 'device123',
            'user_a_mid': 'user_a_1',
            'user_a_member_id': 'member_a_1',
            'user_a_name': '用户A1',
            'user_a_bd': 'BD_A',
            'user_a_level': 'VIP1',
            'user_a_time': '2024-01-01',
            'user_b_mid': 'user_b_1',
            'user_b_member_id': 'member_b_1',
            'user_b_name': '用户B1',
            'user_b_bd': 'BD_B',
            'user_b_level': 'VIP2',
            'user_b_time': '2024-01-02',
            'same_bd': False,
            'match_count': 1
        },
        {
            'relationship_type': 'ip',
            'shared_value': '***********',
            'user_a_mid': 'user_a_2',
            'user_a_member_id': 'member_a_2',
            'user_a_name': '用户A2',
            'user_a_bd': 'BD_A',
            'user_a_level': 'VIP1',
            'user_a_time': '2024-01-03',
            'user_b_mid': 'user_b_2',
            'user_b_member_id': 'member_b_2',
            'user_b_name': '用户B2',
            'user_b_bd': 'BD_A',
            'user_b_level': 'VIP3',
            'user_b_time': '2024-01-04',
            'same_bd': True,
            'match_count': 2
        }
    ]
    
    # 测试批量插入
    try:
        task_repo.batch_insert_shared_relationships(test_task_id, test_relationships)
        print(f"✅ 批量插入共享关系数据成功，共 {len(test_relationships)} 条记录")
    except Exception as e:
        print(f"❌ 批量插入共享关系数据失败: {e}")
        return False
    
    # 验证数据插入
    try:
        result = task_repo.get_shared_relationships(test_task_id)
        relationships = result.get('relationships', [])
        print(f"✅ 查询共享关系数据成功，共 {len(relationships)} 条记录")
        
        # 显示插入的数据
        for i, rel in enumerate(relationships):
            print(f"  记录 {i+1}:")
            print(f"    - 关系类型: {rel.get('relationship_type')}")
            print(f"    - 共享值: {rel.get('shared_value')}")
            print(f"    - 用户A: {rel.get('user_a_name')} ({rel.get('user_a_mid')})")
            print(f"    - 用户B: {rel.get('user_b_name')} ({rel.get('user_b_mid')})")
            print(f"    - 同BD: {rel.get('same_bd')}")
            
    except Exception as e:
        print(f"❌ 查询共享关系数据失败: {e}")
        return False
    
    # 清理测试数据
    try:
        from database.duckdb_manager import db_manager
        db_manager.execute_sql_no_return("DELETE FROM shared_relationships WHERE task_id = ?", [test_task_id])
        db_manager.execute_sql_no_return("DELETE FROM tasks WHERE task_id = ?", [test_task_id])
        print("🗑️  测试数据已清理")
    except Exception as e:
        print(f"⚠️  清理测试数据失败: {e}")
    
    print("✅ 所有测试通过！shared_relationships 表现在可以正常工作了。")
    return True

if __name__ == "__main__":
    test_shared_relationships()
