#!/usr/bin/env python3
"""
测试 incomplete_positions_waiting 表的约束检查修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import db_manager
from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition
from datetime import datetime
import pandas as pd

def test_constraint_validation():
    """测试约束验证逻辑"""
    print("🧪 测试 incomplete_positions_waiting 表约束检查修复...")
    
    # 创建测试数据 - 包含各种边界情况
    test_positions = {
        'valid_position': CompletePosition(
            position_id='valid_001',
            member_id='user_001',
            contract_name='BTC-USDT',
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=1000.0,  # 正数
            total_open_volume=0.1,
            avg_open_price=50000.0,
            open_trades_count=1,
            primary_side=1,
            first_close_time=None,
            last_close_time=None,
            total_close_amount=0.0,
            total_close_volume=0.0,
            avg_close_price=0.0,
            close_trades_count=0,
            is_completed=False,
            total_duration_minutes=0.0,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=1,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[]
        ),
        'zero_amount_position': CompletePosition(
            position_id='zero_001',
            member_id='user_002',
            contract_name='ETH-USDT',
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=0.0,  # 零金额 - 应该被跳过
            total_open_volume=0.0,
            avg_open_price=3000.0,
            open_trades_count=0,
            primary_side=1,
            first_close_time=None,
            last_close_time=None,
            total_close_amount=0.0,
            total_close_volume=0.0,
            avg_close_price=0.0,
            close_trades_count=0,
            is_completed=False,
            total_duration_minutes=0.0,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=0,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[]
        ),
        'negative_amount_position': CompletePosition(
            position_id='negative_001',
            member_id='user_003',
            contract_name='ADA-USDT',
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=-100.0,  # 负数金额 - 应该被跳过
            total_open_volume=0.0,
            avg_open_price=0.5,
            open_trades_count=1,
            primary_side=3,
            first_close_time=None,
            last_close_time=None,
            total_close_amount=0.0,
            total_close_volume=0.0,
            avg_close_price=0.0,
            close_trades_count=0,
            is_completed=False,
            total_duration_minutes=0.0,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=1,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[]
        ),
        'no_open_time_position': CompletePosition(
            position_id='no_time_001',
            member_id='user_004',
            contract_name='DOT-USDT',
            first_open_time=None,  # 无开仓时间 - 应该被跳过
            last_open_time=None,
            total_open_amount=500.0,
            total_open_volume=0.0,
            avg_open_price=10.0,
            open_trades_count=0,
            primary_side=1,
            first_close_time=None,
            last_close_time=None,
            total_close_amount=0.0,
            total_close_volume=0.0,
            avg_close_price=0.0,
            close_trades_count=0,
            is_completed=False,
            total_duration_minutes=0.0,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=0,
            reduce_position_count=0,
            risk_score=0.0,
            abnormal_flags=[]
        )
    }
    
    # 模拟 _add_to_waiting_table 的验证逻辑
    valid_positions = {}
    skipped_positions = {}
    
    for pos_id, position in test_positions.items():
        # 应用修复后的验证逻辑
        total_open_amount = float(position.total_open_amount) if hasattr(position.total_open_amount, 'item') else float(position.total_open_amount)
        
        if (position.first_open_time is None or 
            total_open_amount <= 0 or 
            position.open_trades_count <= 0):
            print(f"❌ 跳过订单 {pos_id}: 开仓时间={position.first_open_time}, 开仓金额={total_open_amount}, 开仓次数={position.open_trades_count}")
            skipped_positions[pos_id] = position
        else:
            print(f"✅ 有效订单 {pos_id}: 开仓时间={position.first_open_time}, 开仓金额={total_open_amount}, 开仓次数={position.open_trades_count}")
            valid_positions[pos_id] = position
    
    print(f"\n📊 验证结果:")
    print(f"  - 有效订单: {len(valid_positions)} 个")
    print(f"  - 跳过订单: {len(skipped_positions)} 个")
    
    # 验证只有 valid_position 应该通过验证
    expected_valid = ['valid_position']
    expected_skipped = ['zero_amount_position', 'negative_amount_position', 'no_open_time_position']
    
    actual_valid = list(valid_positions.keys())
    actual_skipped = list(skipped_positions.keys())
    
    if set(actual_valid) == set(expected_valid) and set(actual_skipped) == set(expected_skipped):
        print("✅ 验证逻辑测试通过！所有边界情况都被正确处理")
        return True
    else:
        print(f"❌ 验证逻辑测试失败！")
        print(f"  期望有效: {expected_valid}")
        print(f"  实际有效: {actual_valid}")
        print(f"  期望跳过: {expected_skipped}")
        print(f"  实际跳过: {actual_skipped}")
        return False

def test_database_constraint():
    """测试数据库约束是否正常工作"""
    print("\n🧪 测试数据库约束...")
    
    # 尝试插入一个 total_open_amount = 0 的记录，应该失败
    test_sql = """
    INSERT INTO incomplete_positions_waiting 
    (position_id, member_id, contract_name, primary_side, first_open_time, 
     total_open_amount, open_trades_count, avg_open_price, total_open_volume,
     waiting_since, last_check_time, check_count)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    test_data = [
        'test_constraint_001',
        'test_user',
        'BTC-USDT',
        1,
        '2024-01-01 10:00:00',
        0.0,  # 这应该触发约束错误
        1,
        50000.0,
        0.1,
        '2024-01-01 10:00:00',
        '2024-01-01 10:00:00',
        0
    ]
    
    try:
        db_manager.execute_sql_no_return(test_sql, test_data)
        print("❌ 数据库约束测试失败：应该拒绝 total_open_amount = 0 的记录")
        return False
    except Exception as e:
        if "CHECK constraint failed" in str(e) and "total_open_amount > 0" in str(e):
            print("✅ 数据库约束测试通过：正确拒绝了 total_open_amount = 0 的记录")
            return True
        else:
            print(f"❌ 数据库约束测试失败：意外的错误类型: {e}")
            return False

if __name__ == "__main__":
    print("🚀 开始测试 incomplete_positions_waiting 表约束检查修复\n")
    
    test1_passed = test_constraint_validation()
    test2_passed = test_database_constraint()
    
    print(f"\n📋 测试总结:")
    print(f"  - 验证逻辑测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  - 数据库约束测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！约束检查修复成功。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
