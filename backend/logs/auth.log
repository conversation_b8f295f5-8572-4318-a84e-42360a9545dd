2025-07-16 00:27:22,020 - modules.auth.utils.error_handler - ERROR - error_handler.py:39 - handle_redis_error - Redis错误 - 操作: test_operation, 错误: 测试Redis连接错误2025-07-18 02:08:44,795 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-21 09:40:58,837 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-21 09:41:14,274 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-21 09:41:14,875 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: pheud4Cw..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-21 09:41:14,875 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-21 09:41:38,369 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=pheud4Cw...
2025-07-21 09:41:40,069 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-21 09:41:40,073 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: pheud4Cw...
2025-07-21 09:41:40,743 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=Af6RDYCB75zOq5vQc6UNc_Z92GYUfmhXYdUTVRsZYe8
2025-07-21 09:41:40,744 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 14:40:04,301 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 14:40:05,226 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: oJba9jd0..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 14:40:40,635 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,971 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,977 - modules.auth.services.totp_service - INFO - totp_service.py:204 - verify_and_enable_2fa - 用户 1 完成2FA首次验证
2025-07-22 14:40:40,978 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: oJba9jd0...
2025-07-22 14:40:40,979 - modules.auth.services.totp_service - ERROR - totp_service.py:209 - verify_and_enable_2fa - 验证并启用2FA失败: TransactionContext Error: Conflict on update!
2025-07-22 14:40:41,832 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 14:40:41,832 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: admin
2025-07-22 14:40:56,165 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 new_user
2025-07-22 14:41:02,810 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 test_user2
2025-07-22 14:43:26,209 - modules.auth.services.totp_service - INFO - totp_service.py:172 - setup_2fa_for_user - 为用户 reveen 设置2FA
2025-07-22 14:43:26,209 - modules.auth.services.auth_service - INFO - auth_service.py:141 - create_user - 创建用户成功: reveen, 角色: admin, 已强制设置2FA
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:27:00,416 - modules.auth.services.session_manager - INFO - session_manager.py:79 - destroy_session - 销毁会话: 6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 15:27:01,107 - modules.auth.api.auth_api - INFO - auth_api.py:295 - logout - 用户登出: admin, IP: 127.0.0.1
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:41:16,470 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 15:41:17,131 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: TH868waq..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:26,765 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:26,766 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:31,848 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:31,849 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:44,791 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 16:04:45,126 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: beEjnXCA..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,597 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 16:04:54,803 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Qi7XsPec..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,803 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-22 16:05:14,373 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,688 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,739 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,739 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Qi7XsPec...
2025-07-22 16:05:14,922 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,923 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:150 - invalidate_temp_token - 临时token失效失败: Qi7XsPec...
2025-07-22 16:05:14,924 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=a2j39K5OF6lEQGbgfONWflOHjkQFmUx2nT16B1qPJ5o
2025-07-22 16:05:14,924 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:15,038 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=UkpD6qKZeETrOws164MxWvB_ZXxWJnW-q3YIP4MOo7s
2025-07-22 16:05:15,039 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:50,941 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_4
2025-07-22 16:05:54,569 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_0
2025-07-22 16:05:58,320 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 totp_test_user
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-23 09:20:09,223 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-07-23 09:20:24,019 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-23 09:20:24,053 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: vo84nUcV..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-23 09:20:45,042 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=vo84nUcV...
2025-07-23 09:20:45,061 - modules.auth.services.totp_service - INFO - totp_service.py:215 - verify_and_enable_2fa - 用户 817184 完成2FA首次验证
2025-07-23 09:20:45,066 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: vo84nUcV...
2025-07-23 09:20:45,090 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=xE2PtInnXeCzz2TiJUq1-r4JQaBu9BJdax9Vzbqaxus
2025-07-23 09:20:45,090 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: bearyang
2025-08-01 23:15:23,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-01 23:15:23,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-01 23:15:55,337 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:04,307 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:14,533 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:36,060 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:48,075 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-01 23:16:48,244 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: yk9SA7So..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-01 23:16:48,244 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-01 23:16:57,401 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=yk9SA7So...
2025-08-01 23:16:57,846 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-01 23:16:57,847 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: yk9SA7So...
2025-08-01 23:16:58,060 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=-m-5z4VXoRosnXqRRD9qvibzo3-L9e-TPw4qhybStfk
2025-08-01 23:16:58,060 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-02 00:18:06,254 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:18:06,254 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:26:51,285 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:26:51,285 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:34:09,286 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:34:09,286 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:39:44,985 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:39:44,985 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:24:06,130 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:24:06,130 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:37:29,108 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:37:29,108 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:39:20,720 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:39:20,720 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:39:24,793 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:39:24,793 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:41:05,131 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:41:05,131 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:51:55,336 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:51:55,336 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:52:11,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:52:11,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:56:36,261 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:56:36,261 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:57:35,814 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:57:35,814 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:59:44,803 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:59:44,803 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:00,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:00,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:02,396 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:02,396 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:27,655 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:27,655 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:01:53,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:01:53,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:02:12,813 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:02:12,813 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:04:46,124 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:04:46,124 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:05:19,410 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:05:19,410 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:06:32,809 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:06:32,809 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:06:49,100 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:06:49,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:08:12,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:08:12,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:20:40,756 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:20:40,756 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:20:55,389 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:20:55,389 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 16:03:49,278 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 16:03:49,278 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 17:47:40,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 17:47:40,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 20:01:23,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 20:01:23,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:13:50,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 23:13:50,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:31:42,155 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 23:31:42,155 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:31:55,916 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-02 23:31:56,154 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: AZSyIRLH..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-02 23:31:56,154 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-02 23:32:12,194 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=AZSyIRLH...
2025-08-02 23:32:12,799 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-02 23:32:12,800 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: AZSyIRLH...
2025-08-02 23:32:13,073 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=WtVepILr-Ycd9UkXc2JcY2PbBuep5A8x-rSJ7AW8wcA
2025-08-02 23:32:13,073 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-03 00:35:23,475 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 00:35:23,475 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 01:28:56,125 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 01:28:56,125 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 11:51:00,138 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 11:51:00,138 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:02:09,409 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:02:09,409 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:11:32,137 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:11:32,137 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:15:10,506 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:15:10,506 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:17:52,486 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:17:52,486 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:40:12,298 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:40:12,298 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:47:13,533 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:47:13,533 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 13:03:59,965 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 13:03:59,965 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 14:42:46,833 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 14:42:46,833 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 15:47:19,622 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 15:47:19,622 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 15:49:51,255 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 15:49:51,256 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 16:57:30,305 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 16:57:30,305 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:33:55,195 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:33:55,196 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:47:20,707 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:47:20,707 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:47:36,725 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:47:36,726 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:48:16,523 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-08-03 17:49:29,659 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-03 17:49:35,403 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-03 17:50:51,932 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:50:52,361 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: HiXR0aoB..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:51:06,251 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817187, token=HiXR0aoB...
2025-08-03 17:51:06,579 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin2, IP: 127.0.0.1, 原因: 2FA验证失败
2025-08-03 17:52:16,435 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:52:16,851 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: MJolIDkM..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:52:27,869 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:52:27,869 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:52:46,598 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:52:47,029 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Ai5SdeJ_..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:52:54,255 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: viewer1, IP: 127.0.0.1
2025-08-03 17:52:54,661 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: hI-2RrOk..., 用户ID: 817185, 过期时间: 300秒, 结果: 成功
2025-08-03 17:54:27,592 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: viewer1, IP: 127.0.0.1
2025-08-03 17:54:27,870 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 viewer1 未启用2FA但系统未强制要求
2025-08-03 17:54:40,734 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:54:41,161 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: DFPIj79q..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 19:44:06,601 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 19:44:06,601 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:03:57,552 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:03:57,552 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:04:14,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:04:14,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:06:05,002 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:06:05,002 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:09:42,530 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:09:42,530 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:12:07,017 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:12:07,017 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:39:09,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:39:09,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 22:30:29,966 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 22:30:29,967 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 22:45:00,483 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 22:45:00,483 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 23:50:36,815 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 23:50:36,815 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 23:50:57,158 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-03 23:50:57,429 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: M3CvI6wJ..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-03 23:50:57,429 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-03 23:51:36,306 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=M3CvI6wJ...
2025-08-03 23:51:37,031 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-03 23:51:37,032 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: M3CvI6wJ...
2025-08-03 23:51:37,374 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=LVthRe6LbUWf5bHLn8W_ef48CR5iL36i6JWD4kdu-B8
2025-08-03 23:51:37,374 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-04 00:41:23,142 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 00:41:23,142 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:02:31,792 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:02:31,921 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:07:22,515 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:07:22,515 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:09:02,096 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:09:02,096 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 15:14:28,861 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 15:14:29,020 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 09:09:00,271 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-08-05 09:09:03,208 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-05 09:09:03,552 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Ex5aJUmq..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-05 09:09:03,552 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-05 09:09:37,877 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=Ex5aJUmq...
2025-08-05 09:09:38,707 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-05 09:09:38,712 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Ex5aJUmq...
2025-08-05 09:09:39,072 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=Ns8yh1sNy4M3SVnHXTOqylfx58waB4jPZrGe2RDa84s
2025-08-05 09:09:39,072 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-05 09:21:56,111 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 09:21:56,111 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 10:50:14,740 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 10:50:14,740 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 14:26:08,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 14:26:08,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-06 23:13:54,222 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-06 23:13:54,223 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-06 23:49:21,871 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-06 23:49:21,871 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 00:07:52,309 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 00:07:52,309 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 00:23:58,046 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 00:23:58,046 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 00:34:17,885 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 00:34:17,886 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 00:36:42,568 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 00:36:42,671 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 01:01:51,308 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 01:01:51,308 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 16:10:34,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 16:10:34,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 17:56:00,014 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 17:56:00,014 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 18:01:02,652 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 18:01:02,652 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 18:01:07,843 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-08-07 18:01:12,330 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-07 18:01:12,583 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: FnRDJa0p..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-07 18:01:12,583 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-07 18:01:30,105 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=FnRDJa0p...
2025-08-07 18:01:30,714 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-07 18:01:30,715 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: FnRDJa0p...
2025-08-07 18:01:30,986 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=oPycjb2G69YFMD7RNgogGIPnGjfnOMRJhjGBTIMPB80
2025-08-07 18:01:30,986 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-07 18:43:34,464 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 18:43:34,464 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 18:43:54,536 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 18:43:54,633 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 18:56:30,402 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 18:56:30,502 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 18:57:07,121 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 18:57:07,223 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-07 19:12:15,230 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-07 19:12:15,230 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-08 09:46:40,324 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-08 09:46:40,324 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-09 23:13:54,678 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-09 23:13:54,678 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-09 23:14:01,380 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-08-09 23:14:04,064 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-09 23:14:04,284 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: l__83MgS..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-09 23:14:04,284 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-09 23:14:30,302 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=l__83MgS...
2025-08-09 23:14:30,910 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-09 23:14:30,912 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: l__83MgS...
2025-08-09 23:14:31,180 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=K8BiyUE9n_Mgu5VnFBMS7YbKJx3Tzv_FN30YuLnorzY
2025-08-09 23:14:31,180 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-09 23:28:30,073 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-09 23:28:30,073 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-09 23:37:20,855 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-09 23:37:20,855 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-09 23:59:16,371 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-09 23:59:16,371 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 13:30:53,269 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 13:30:53,269 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 13:40:49,967 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 13:40:49,968 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 13:43:22,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 13:43:22,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 14:04:25,502 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 14:04:25,502 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 14:46:46,671 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 14:46:46,671 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 14:46:53,832 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: IO Error: Could not set lock on file "/Users/<USER>/Downloads/risk-link-analysis-tool/data/risk_analysis.duckdb": Resource temporarily unavailable
2025-08-10 14:46:53,854 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: IO Error: Could not set lock on file "/Users/<USER>/Downloads/risk-link-analysis-tool/data/risk_analysis.duckdb": Resource temporarily unavailable
2025-08-10 14:47:06,002 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 14:47:06,103 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 15:09:24,615 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 15:09:24,615 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 17:29:14,228 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 17:29:14,228 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 17:39:13,404 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 17:39:13,405 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 18:10:39,626 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 18:10:39,626 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 18:54:11,102 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 18:54:11,102 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 20:44:36,910 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 20:44:36,910 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 21:12:49,621 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 21:12:49,622 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 21:25:29,394 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 21:25:29,394 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 21:41:16,968 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 21:41:16,968 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 22:37:40,877 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 22:37:40,877 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 22:52:54,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 22:52:54,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 23:08:03,904 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 23:08:03,904 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 23:16:29,063 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-08-10 23:43:19,577 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-10 23:43:19,577 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-10 23:44:51,825 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-10 23:44:52,059 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: XJ5W-gWW..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-10 23:44:52,059 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-10 23:45:10,166 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=XJ5W-gWW...
2025-08-10 23:45:10,767 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-10 23:45:10,767 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: XJ5W-gWW...
2025-08-10 23:45:11,063 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=1R4fmstMiyaYTYlA2aUB2tXrgBKvgMElkkLS7K_0j9U
2025-08-10 23:45:11,063 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-11 00:00:38,822 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 00:00:38,822 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 00:50:33,018 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 00:50:33,121 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 01:02:26,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 01:02:26,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 01:10:07,687 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 01:10:07,788 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 01:13:32,116 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 01:13:32,116 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 01:28:49,581 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 01:28:49,582 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 09:32:17,159 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 09:32:17,159 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 09:40:47,587 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 09:40:47,587 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 10:33:15,221 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 10:33:15,221 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 12:11:07,494 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 12:11:07,494 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 14:28:31,215 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 14:28:31,215 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 14:49:12,958 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 用户不存在
2025-08-11 14:49:17,958 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 用户不存在
2025-08-11 14:55:30,925 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 14:55:30,925 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 14:55:33,457 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-11 14:55:37,990 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-11 14:55:47,644 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-11 14:55:55,079 - modules.auth.services.auth_service - WARNING - auth_service.py:320 - _handle_failed_login - 账户锁定: bearyang, 锁定到: 2025-08-11 15:25:55.071521
2025-08-11 14:55:55,079 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-11 14:56:19,257 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 14:56:19,257 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 14:56:26,793 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 账户被锁定
2025-08-11 14:56:33,192 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 账户被锁定
2025-08-11 15:23:53,265 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 15:23:53,277 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 bearyang 未启用2FA但系统未强制要求
2025-08-11 15:24:01,361 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 15:24:01,369 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 bearyang 未启用2FA但系统未强制要求
2025-08-11 15:24:26,477 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 15:24:26,488 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 bearyang 未启用2FA但系统未强制要求
2025-08-11 17:18:22,830 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 17:18:22,840 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 bearyang 未启用2FA但系统未强制要求
2025-08-11 17:21:03,494 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 17:21:03,507 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Dh65lIEU..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-11 17:21:03,507 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-11 17:21:35,606 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=Dh65lIEU...
2025-08-11 17:21:35,651 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-11 17:21:35,652 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Dh65lIEU...
2025-08-11 17:21:35,668 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=JR9tbN3gwL3AZQxk4VG2O0r3oqsCxZHup1r9E5LHWv4
2025-08-11 17:21:35,668 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-11 17:21:57,000 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 17:21:57,000 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 17:57:20,665 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 17:57:20,665 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 18:08:43,498 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 18:08:43,498 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 18:19:18,211 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 18:19:18,211 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 18:40:45,517 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 18:40:45,517 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 21:30:39,379 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 21:30:39,379 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 22:09:14,813 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: Catalog Error: Table with name auth_user_sessions does not exist!
Did you mean "pg_settings"?
2025-08-11 22:09:14,813 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: Catalog Error: Table with name auth_user_sessions does not exist!
Did you mean "pg_settings"?
2025-08-11 22:09:16,242 - modules.auth.services.auth_service - ERROR - auth_service.py:64 - authenticate_user - 用户认证失败: Catalog Error: Table with name auth_users does not exist!
Did you mean "tasks"?
LINE 1: SELECT * FROM auth_users WHERE username = ?
                      ^
2025-08-11 22:09:20,430 - modules.auth.services.auth_service - ERROR - auth_service.py:64 - authenticate_user - 用户认证失败: Catalog Error: Table with name auth_users does not exist!
Did you mean "tasks"?
LINE 1: SELECT * FROM auth_users WHERE username = ?
                      ^
2025-08-11 22:25:03,550 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 22:25:03,570 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: RDfxZqwK..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-11 22:25:03,570 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-11 22:25:15,864 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=RDfxZqwK...
2025-08-11 22:25:15,919 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-11 22:25:15,920 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: RDfxZqwK...
2025-08-11 22:25:15,938 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=pM2ED8emjTMsruiGsTOhoYdlDrFARVXtmiu15cDaRww
2025-08-11 22:25:15,938 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-11 22:38:20,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 22:38:20,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:10:18,536 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:10:18,536 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:10:23,470 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: Binder Error: Table "s" does not have a column named "session_id"
LINE 5:             WHERE s.session_id = ? AND s.is_active = true
                          ^
2025-08-11 23:11:57,152 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:11:57,152 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:12:12,165 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:12:12,166 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:13:25,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:13:25,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:13:40,232 - modules.auth.services.auth_service - ERROR - auth_service.py:64 - authenticate_user - 用户认证失败: Binder Error: Referenced update column last_login not found in table!
2025-08-11 23:13:44,139 - modules.auth.services.auth_service - ERROR - auth_service.py:64 - authenticate_user - 用户认证失败: Binder Error: Referenced update column last_login not found in table!
2025-08-11 23:15:05,575 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-11 23:15:40,461 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:15:40,461 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:15:42,248 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:15:42,248 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:15:53,253 - modules.auth.services.totp_service - ERROR - totp_service.py:328 - get_user_2fa_status - 获取2FA状态失败: Binder Error: Referenced column "is_2fa_verified" not found in FROM clause!
Candidate bindings: "auth_users.is_2fa_enabled"
LINE 2:             SELECT is_2fa_enabled, is_2fa_verified, last_2fa_verify, back...
                                           ^
2025-08-11 23:15:53,263 - modules.auth.services.auth_service - ERROR - auth_service.py:243 - log_user_activity - 记录活动日志失败: Binder Error: Table "auth_user_activity_logs" does not have a column with name "error_message"
2025-08-11 23:15:53,263 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 23:15:53,316 - modules.auth.services.totp_service - ERROR - totp_service.py:180 - setup_2fa_for_user - 设置2FA失败: Binder Error: Referenced update column backup_codes not found in table!
2025-08-11 23:15:53,316 - modules.auth.api.auth_api - ERROR - auth_api.py:87 - login - 强制设置2FA失败: Binder Error: Referenced update column backup_codes not found in table!
2025-08-11 23:16:00,480 - modules.auth.services.totp_service - ERROR - totp_service.py:328 - get_user_2fa_status - 获取2FA状态失败: Binder Error: Referenced column "is_2fa_verified" not found in FROM clause!
Candidate bindings: "auth_users.is_2fa_enabled"
LINE 2:             SELECT is_2fa_enabled, is_2fa_verified, last_2fa_verify, back...
                                           ^
2025-08-11 23:16:00,488 - modules.auth.services.auth_service - ERROR - auth_service.py:243 - log_user_activity - 记录活动日志失败: Binder Error: Table "auth_user_activity_logs" does not have a column with name "error_message"
2025-08-11 23:16:00,488 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 23:16:00,509 - modules.auth.services.totp_service - ERROR - totp_service.py:180 - setup_2fa_for_user - 设置2FA失败: Binder Error: Referenced update column backup_codes not found in table!
2025-08-11 23:16:00,509 - modules.auth.api.auth_api - ERROR - auth_api.py:87 - login - 强制设置2FA失败: Binder Error: Referenced update column backup_codes not found in table!
2025-08-11 23:17:11,968 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-11 23:17:11,993 - modules.auth.services.totp_service - INFO - totp_service.py:171 - setup_2fa_for_user - 为用户 bearyang 设置2FA
2025-08-11 23:17:11,997 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: XYGU9cVG..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-08-11 23:17:11,997 - modules.auth.api.auth_api - INFO - auth_api.py:76 - login - 为用户 bearyang 强制设置2FA
2025-08-11 23:18:25,225 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=XYGU9cVG...
2025-08-11 23:18:51,401 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=XYGU9cVG...
2025-08-11 23:18:51,419 - modules.auth.services.totp_service - INFO - totp_service.py:215 - verify_and_enable_2fa - 用户 1 完成2FA首次验证
2025-08-11 23:18:51,429 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: XYGU9cVG...
2025-08-11 23:18:51,454 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=PhLNop4jSSGceRUVm6525agKHGB0yfHOOVZzoww-T3s
2025-08-11 23:18:51,455 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: bearyang
2025-08-11 23:19:07,470 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:19:07,470 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:35:07,902 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:35:07,906 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:45:51,134 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:45:51,134 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:46:06,950 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:46:06,950 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:47:58,314 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:47:58,314 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:48:05,100 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:48:05,100 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-11 23:48:10,590 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-11 23:48:10,593 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 00:00:02,290 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 00:00:02,290 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 00:06:21,949 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 00:06:21,949 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 00:06:45,099 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 00:06:45,103 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 00:46:52,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 00:46:52,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 00:56:32,172 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 00:56:32,172 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 01:07:32,027 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 01:07:32,027 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 01:37:41,457 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 01:37:41,457 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 09:20:22,350 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 09:20:22,350 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 09:20:41,859 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 09:20:41,859 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 10:16:18,527 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 10:16:18,527 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-12 10:27:21,952 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-12 10:27:21,952 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-13 08:49:10,166 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-13 08:49:10,172 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-13 10:13:49,729 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-13 10:13:49,729 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-13 10:14:18,362 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-13 10:14:18,386 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: AOP5AFvw..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-13 10:14:18,386 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-13 10:14:49,585 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=AOP5AFvw...
2025-08-13 10:14:49,646 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-13 10:14:49,649 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: AOP5AFvw...
2025-08-13 10:14:49,671 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=A_wVzWXrm2DVmJRU3djCGscvGbE9mzFbQiLQ7fsnbc4
2025-08-13 10:14:49,671 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-13 10:20:33,574 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-13 10:20:33,590 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: 6q1LStdv..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-13 10:20:33,590 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-13 10:20:47,815 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=6q1LStdv...
2025-08-13 10:20:47,883 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-13 10:20:47,884 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: 6q1LStdv...
2025-08-13 10:20:47,905 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=t3Y7BQFn-6UzC3znzq9AhXegePXXErfkiORGY9Ek_qc
2025-08-13 10:20:47,905 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-14 09:07:55,885 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 09:07:55,886 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 09:30:31,174 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 09:30:31,174 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 09:49:45,579 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 09:49:45,579 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 09:50:01,641 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 09:50:01,641 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 10:21:20,093 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 10:21:20,115 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: AVL9lrPh..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 10:21:20,115 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-14 10:21:42,758 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=AVL9lrPh...
2025-08-14 10:21:42,798 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 2FA验证失败
2025-08-14 10:21:51,220 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=AVL9lrPh...
2025-08-14 10:21:51,312 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-14 10:21:51,313 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: AVL9lrPh...
2025-08-14 10:21:51,344 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=IU6AytNSu4BhB7lE8NeCohqmdk1Erh3RGG9tXBumPFk
2025-08-14 10:21:51,344 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-14 11:12:29,447 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 11:12:29,447 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 11:24:14,643 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 11:24:14,643 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 11:24:27,856 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 11:24:27,856 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 13:18:41,576 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 13:18:41,576 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 13:18:50,138 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 13:18:50,159 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: jQEaXhWT..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 13:18:50,159 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-14 13:19:06,653 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=jQEaXhWT...
2025-08-14 13:19:06,725 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-14 13:19:06,725 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: jQEaXhWT...
2025-08-14 13:19:06,745 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=9zk-Ifmx8Gu6dM2MR6ngXeN78OhDKgqKe1iIZCIV800
2025-08-14 13:19:06,745 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-14 13:23:58,702 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 13:23:58,702 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 13:52:01,643 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 13:52:01,643 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 13:52:16,309 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 13:52:16,309 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 14:15:32,762 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 14:15:32,762 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 14:17:14,228 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 14:17:14,228 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 14:18:32,678 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 14:18:32,678 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 14:34:33,207 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 14:34:33,207 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 15:13:33,822 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-08-14 15:15:03,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 15:15:03,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 15:15:26,343 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 15:15:26,365 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: 0OlWTmRS..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 15:15:26,365 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-14 15:15:57,758 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 15:15:57,776 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: icnC38og..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 15:15:57,776 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-14 21:12:57,099 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 21:12:57,099 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 21:41:43,635 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 21:41:43,636 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:32:04,027 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:32:04,027 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:32:09,542 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: Binder Error: Table "s" does not have a column named "is_active"

Candidate bindings: : "last_activity"

LINE 5:             WHERE s.session_id = ? AND s.is_active = true
                                               ^
2025-08-14 22:36:58,926 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:36:58,926 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:37:24,013 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:37:24,013 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:38:03,268 - modules.auth.services.auth_service - ERROR - auth_service.py:64 - authenticate_user - 用户认证失败: 'locked_until'
2025-08-14 22:41:46,828 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:41:46,829 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:43:20,435 - modules.auth.services.auth_service - ERROR - auth_service.py:243 - log_user_activity - 记录活动日志失败: Binder Error: Table "auth_user_activity_logs" does not have a column with name "resource"
2025-08-14 22:43:20,435 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 22:43:20,500 - modules.auth.services.totp_service - INFO - totp_service.py:171 - setup_2fa_for_user - 为用户 bearyang 设置2FA
2025-08-14 22:43:20,502 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: HxTPsN9X..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 22:43:20,502 - modules.auth.api.auth_api - INFO - auth_api.py:76 - login - 为用户 bearyang 强制设置2FA
2025-08-14 22:45:33,951 - modules.auth.services.auth_service - ERROR - auth_service.py:243 - log_user_activity - 记录活动日志失败: Binder Error: Table "auth_user_activity_logs" does not have a column with name "resource"
2025-08-14 22:45:33,951 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 22:45:33,981 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: 8tz9vnrB..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 22:45:45,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:45:45,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 22:46:10,557 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 22:46:10,557 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 23:08:27,523 - modules.auth.services.auth_service - ERROR - auth_service.py:243 - log_user_activity - 记录活动日志失败: Binder Error: Table "auth_user_activity_logs" does not have a column with name "resource"
2025-08-14 23:08:27,524 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-14 23:08:27,564 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: fExr1g9R..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-14 23:29:02,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 23:29:02,617 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-14 23:29:30,025 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-14 23:29:30,025 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 01:19:43,621 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 01:19:43,621 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 01:20:15,034 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-15 01:20:15,053 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: WNsNtyTe..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-15 01:20:15,053 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-15 01:42:50,029 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 01:42:50,029 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 01:43:40,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 01:43:40,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:08:09,294 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: WNsNtyTe...
2025-08-15 02:08:18,672 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: WNsNtyTe...
2025-08-15 02:08:36,217 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: WNsNtyTe...
2025-08-15 02:08:43,576 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: WNsNtyTe...
2025-08-15 02:08:59,517 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-15 02:08:59,534 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: WvOXs0q3..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-15 02:08:59,534 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-15 02:09:10,040 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=WvOXs0q3...
2025-08-15 02:09:10,078 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 2FA验证失败
2025-08-15 02:09:23,462 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=WvOXs0q3...
2025-08-15 02:09:23,527 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-15 02:09:23,528 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: WvOXs0q3...
2025-08-15 02:09:23,548 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=UxaQ2WMQV4q3PwRMZyopQ5Hw305jV4KrZi8DOXXkRnA
2025-08-15 02:09:23,548 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-15 02:10:22,468 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:10:22,468 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:12:02,326 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:12:02,326 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:17:25,928 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:17:25,928 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:20:54,462 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:20:54,463 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:24:16,916 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:24:16,916 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:25:29,967 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:25:29,967 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-15 02:30:07,628 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-15 02:30:07,628 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
