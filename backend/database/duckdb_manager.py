#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DuckDB数据库管理器
负责数据库连接、初始化和基本操作
"""

import os
import duckdb
import logging
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

# 导入统一的数据库配置
from config.database_config import db_config, validate_db_path

logger = logging.getLogger(__name__)

class DuckDBManager:
    """DuckDB数据库管理器"""

    def __init__(self, db_path: str = None):
        if db_path is None:
            # 使用统一配置的主数据库路径
            self.db_path = db_config.get_main_db_path()
        else:
            # 验证提供的数据库路径
            is_valid, error_msg = validate_db_path(db_path)
            if not is_valid:
                logger.warning(f"数据库路径验证失败: {error_msg}")
                logger.warning(f"使用默认路径: {db_config.get_main_db_path()}")
                self.db_path = db_config.get_main_db_path()
            else:
                self.db_path = db_path

        # 确保数据库目录存在
        self.ensure_db_directory()

        # 记录使用的数据库路径（改为DEBUG级别，减少日志输出）
        logger.debug(f"DuckDB管理器初始化，数据库路径: {self.db_path}")
        
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 使用配置中的连接参数
            connection_config = db_config.get_connection_config()
            max_retries = connection_config['max_retries']
            retry_delay = connection_config['retry_delay']

            import time
            for attempt in range(max_retries):
                try:
                    conn = duckdb.connect(self.db_path)
                    # DuckDB不支持busy_timeout，使用其他方式处理并发
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"数据库连接失败，重试 {attempt + 1}/{max_retries}: {e}")
                        time.sleep(retry_delay)
                    else:
                        logger.error(f"数据库连接最终失败: {e}")
                        raise e

            yield conn
        finally:
            if conn:
                conn.close()
    
    def execute_sql(self, sql: str, params: Optional[List] = None) -> List[Dict[str, Any]]:
        """执行SQL查询并返回结果"""
        try:
            with self.get_connection() as conn:
                if params:
                    result = conn.execute(sql, params)
                else:
                    result = conn.execute(sql)
                
                # 获取列名
                columns = [desc[0] for desc in result.description] if result.description else []
                
                # 获取数据并转换为字典列表
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]
        except Exception as e:
            logger.error(f"SQL执行失败: {sql}, 错误: {e}")
            raise
    
    def execute_query(self, sql: str, params: Optional[List] = None) -> List[tuple]:
        """执行SQL查询并返回结果（兼容现有API调用）"""
        try:
            with self.get_connection() as conn:
                if params:
                    result = conn.execute(sql, params)
                else:
                    result = conn.execute(sql)
                return result.fetchall()
        except Exception as e:
            logger.error(f"SQL执行失败: {sql}, 错误: {e}")
            raise
            
    def execute_sql_no_return(self, sql: str, params: Optional[List] = None) -> None:
        """执行SQL语句（不返回结果，用于INSERT/UPDATE/DELETE）"""
        try:
            with self.get_connection() as conn:
                if params:
                    conn.execute(sql, params)
                else:
                    conn.execute(sql)
        except Exception as e:
            logger.error(f"SQL执行失败: {sql}, 错误: {e}")
            raise
    
    def fetch_all(self, sql: str, params: Optional[List] = None) -> List[Dict[str, Any]]:
        """执行查询并返回所有结果"""
        return self.execute_sql(sql, params)
    
    def fetch_one(self, sql: str, params: Optional[List] = None) -> Optional[Dict[str, Any]]:
        """执行查询并返回第一条结果"""
        results = self.execute_sql(sql, params)
        return results[0] if results else None
    
    def execute_many(self, sql: str, params_list: List[List]) -> None:
        """批量执行SQL语句"""
        try:
            with self.get_connection() as conn:
                # 使用事务提高性能并减少锁定时间
                conn.execute("BEGIN TRANSACTION")
                try:
                    conn.executemany(sql, params_list)
                    conn.execute("COMMIT")
                except Exception as e:
                    conn.execute("ROLLBACK")
                    raise e
        except Exception as e:
            logger.error(f"批量SQL执行失败: {sql}, 错误: {e}")
            raise
    
    def execute_script(self, sql_script: str) -> None:
        """执行SQL脚本"""
        try:
            with self.get_connection() as conn:
                # 分割多个SQL语句并执行
                statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
                for statement in statements:
                    if statement:
                        conn.execute(statement)
        except Exception as e:
            logger.error(f"SQL脚本执行失败: {e}")
            raise
    
    def initialize_database(self):
        """初始化数据库schema"""
        schema_sql = """
        -- 用户基础信息表
        CREATE TABLE IF NOT EXISTS users (
            digital_id VARCHAR PRIMARY KEY,
            member_id VARCHAR UNIQUE NOT NULL,
            user_name VARCHAR,
            agent_flag VARCHAR,  -- BD, 1级代理, 2级代理, 3级代理, 直客
            analyzed_level_number INTEGER,
            analyzed_level_type VARCHAR,
            bd_name VARCHAR,
            device_count INTEGER DEFAULT 0,
            last_login TIMESTAMP,
            risk_level VARCHAR,
            risk_score DECIMAL(10,2),
            relation_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 用户关系表
        CREATE TABLE IF NOT EXISTS user_relationships (
            id INTEGER PRIMARY KEY,
            parent_digital_id VARCHAR,
            child_digital_id VARCHAR,
            relationship_type VARCHAR,  -- direct_agent, sub_agent, etc.
            level_diff INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(parent_digital_id, child_digital_id)
        );

        -- 注意：contract_risk_analysis 表和相关序列已被移除
        -- 所有合约风险分析数据现在存储在新的存储系统中：
        -- - algorithm_results: 算法结果主表
        -- - contract_risk_details: 风险详情表
        -- - wash_trading_pairs: 对敲交易详情表

        -- 任务管理表序列
        CREATE SEQUENCE IF NOT EXISTS tasks_id_seq;

        -- 任务管理表
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY DEFAULT nextval('tasks_id_seq'),
            task_id VARCHAR(50) UNIQUE NOT NULL,
            task_type VARCHAR(50) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            filename VARCHAR,
            progress INTEGER DEFAULT 0,
            message TEXT,
            parameters JSON,
            result JSON,
            result_summary JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP
        );



        -- 代理关系分析表序列
        CREATE SEQUENCE IF NOT EXISTS agent_analysis_id_seq;
        
        -- 代理关系分析表
        CREATE TABLE IF NOT EXISTS agent_analysis (
            id INTEGER PRIMARY KEY DEFAULT nextval('agent_analysis_id_seq'),
            task_id VARCHAR UNIQUE NOT NULL,
            analysis_type VARCHAR,
            filename VARCHAR,
            total_users INTEGER DEFAULT 0,
            device_shared_count INTEGER DEFAULT 0,
            ip_shared_count INTEGER DEFAULT 0,
            both_shared_count INTEGER DEFAULT 0,
            result_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 🆕 共享关系详情表（解决内存溢出问题）
        CREATE TABLE IF NOT EXISTS shared_relationships (
            id INTEGER PRIMARY KEY,
            task_id VARCHAR NOT NULL,
            relationship_type VARCHAR NOT NULL,  -- 'device', 'ip', 'both'
            shared_value VARCHAR,  -- 共享的设备ID或IP
            user_a_mid VARCHAR,
            user_a_member_id VARCHAR,
            user_a_name VARCHAR,
            user_a_bd VARCHAR,
            user_a_level VARCHAR,
            user_a_time VARCHAR,
            user_b_mid VARCHAR,
            user_b_member_id VARCHAR,
            user_b_name VARCHAR,
            user_b_bd VARCHAR,
            user_b_level VARCHAR,
            user_b_time VARCHAR,
            same_bd BOOLEAN,
            match_count INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_users_agent_flag ON users(agent_flag);
        CREATE INDEX IF NOT EXISTS idx_users_bd_name ON users(bd_name);
        CREATE INDEX IF NOT EXISTS idx_users_risk_level ON users(risk_level);
        CREATE INDEX IF NOT EXISTS idx_users_member_id ON users(member_id);
        CREATE INDEX IF NOT EXISTS idx_relationships_parent ON user_relationships(parent_digital_id);
        CREATE INDEX IF NOT EXISTS idx_relationships_child ON user_relationships(child_digital_id);
        CREATE INDEX IF NOT EXISTS idx_relationships_type ON user_relationships(relationship_type);
        CREATE INDEX IF NOT EXISTS idx_tasks_type_status ON tasks(task_type, status);
        CREATE INDEX IF NOT EXISTS idx_tasks_created ON tasks(created_at);

        CREATE INDEX IF NOT EXISTS idx_agent_analysis_task ON agent_analysis(task_id);
        CREATE INDEX IF NOT EXISTS idx_agent_analysis_created ON agent_analysis(created_at);
        -- 🆕 共享关系表索引
        CREATE INDEX IF NOT EXISTS idx_shared_relationships_task ON shared_relationships(task_id);
        CREATE INDEX IF NOT EXISTS idx_shared_relationships_type ON shared_relationships(relationship_type);
        CREATE INDEX IF NOT EXISTS idx_shared_relationships_bd ON shared_relationships(user_a_bd, user_b_bd);
        CREATE INDEX IF NOT EXISTS idx_shared_relationships_created ON shared_relationships(created_at);
        """
        
        try:
            self.execute_script(schema_sql)
            logger.info("数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def clear_all_data(self):
        """清空所有数据表（保留结构）"""
        try:
            tables = ['agent_analysis', 'user_relationships', 'users', 'tasks', 'shared_relationships']
            for table in tables:
                self.execute_sql(f"DELETE FROM {table}")
            logger.info("所有数据已清空")
        except Exception as e:
            logger.error(f"清空数据失败: {e}")
            raise
    
    def get_table_stats(self) -> Dict[str, int]:
        """获取各表的记录数统计"""
        stats = {}
        tables = ['users', 'user_relationships', 'tasks', 'agent_analysis', 'shared_relationships']

        for table in tables:
            try:
                result = self.execute_sql(f"SELECT COUNT(*) as count FROM {table}")
                stats[table] = result[0]['count'] if result else 0
            except Exception as e:
                logger.error(f"获取表{table}统计失败: {e}")
                stats[table] = 0

        return stats

# 创建全局数据库管理器实例
db_manager = DuckDBManager() 